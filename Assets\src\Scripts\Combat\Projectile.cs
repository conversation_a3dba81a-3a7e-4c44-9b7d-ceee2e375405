using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(Collider2D))]
public class Projectile : MonoBehaviour, ISpawnable, ICollisionHandler
{
    [Title("Projectile Settings")]
    [SerializeField] private float defaultSpeed = 10f;
    [SerializeField] private float defaultLifetime = 2f; // Changed from range to lifetime
    [SerializeField] private float baseDamage = 10f;
    
    [Title("Debug Settings")]
    [SerializeField] private bool enableForkDebugLogging = true;
    [Tooltip("Enable debug logging for fork projectile status effect issues")]

    [Title("Particle Effects")]
    [SerializeField] private bool useImpactParticles = true;
    [ShowIf("useImpactParticles")]
    [SerializeField] private ParticleType impactParticleType = ParticleType.SparkImpact;
    [ShowIf("useImpactParticles")]
    [SerializeField] private int impactParticleCount = 10;
    
    [SerializeField] private bool useTrailParticles = false;
    [ShowIf("useTrailParticles")]
    [SerializeField] private ParticleType trailParticleType = ParticleType.FireTrail;
    [ShowIf("useTrailParticles")]
    [SerializeField] private float trailInterval = 0.1f;
    
    [SerializeField] private bool useDespawnParticles = true;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private ParticleType despawnParticleType = ParticleType.SmokeImpact;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private int despawnParticleCount = 5;
    
    [InfoBox("Optional: Assign a child transform for precise particle spawn position")]
    [SerializeField] private Transform particleSpawnPoint;
    
    // Runtime values
    public float speed { get; set; }
    public float lifetime { get; set; } // Changed from maxRange to lifetime
    public float damage { get; set; }
    public float critChance { get; set; }
    public float critMultiplier { get; set; }
    public DamageType damageType { get; set; } = DamageType.Physical;
    public float ailmentChance { get; set; } = 0f;

    // Gem data for status effect configuration
    public SkillGemData skillGemData { get; set; }
    public System.Collections.Generic.List<GemInstance> supportGems { get; set; }
    
    // Damage breakdown for type-specific damage
    public DamageBreakdown? damageBreakdown { get; set; }
    
    // Support gem effects
    private bool isPiercing;
    private int pierceCount;
    private int currentPierces;
    
    private bool isChaining;
    private int chainCount;
    private int currentChains;
    private GameObject lastTarget;
    
    private bool isFork;
    private int forkCount;
    private float forkAngle;
    private bool hasForked;
    
    private bool hasAreaDamage;
    private float areaRadius;
    
    // Store current critical hit result to share between main hit and area damage
    private bool _currentCritResult;
    private bool _hasRolledCrit; // Track if we've already rolled crit for this projectile
     
    private Vector2 _direction;
    private float _timeAlive; // Changed from _distanceTraveled
    private Collider2D _collider2D;
    private bool _isActive;
    private LayerMask _currentLayerMask;
    
    // Cache for nearby enemies to eliminate allocations in collision detection
    private readonly System.Collections.Generic.List<ICollidable> _nearbyEnemiesCache = new System.Collections.Generic.List<ICollidable>(32);
    
    private void Awake()
    {
        _collider2D = GetComponent<Collider2D>();
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageValue = 1f, int layer = 10)
    {
        Initialize(position, direction, damageValue, layer, defaultSpeed, defaultLifetime);
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageValue, int layer, float projectileSpeed, float projectileLifetime)
    {
        transform.position = position;
        _direction = direction.normalized;
        _timeAlive = 0f;
        // Use the final calculated damage directly instead of multiplying by prefab's baseDamage
        this.damage = damageValue;
        this.speed = projectileSpeed;
        this.lifetime = projectileLifetime;
        _isActive = true;
        
        gameObject.layer = layer;
        _currentLayerMask = 1 << layer;
        
        // Rotate to face direction
        float angle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        
        // Start trail particles if enabled
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StartContinuousEffect(trailParticleType, transform, trailInterval);
        }

        // DEBUG: Log initialization values
        Debug.Log($"[Projectile] {name} Initialized - Position: {position}, Direction: {_direction}, Speed: {speed}, Damage: {damage}, Active: {_isActive}, Layer: {layer}");
    }
    
    private void Update()
    {
        if (!_isActive)
        {
            // DEBUG: Log why projectile is not active
            Debug.Log($"[Projectile] {name} Update skipped - _isActive: {_isActive}");
            return;
        }

        // Calculate next position using frame-rate independent timing
        Vector2 currentPos = transform.position;
        Vector2 movement = _direction * speed * Time.deltaTime;
        Vector2 nextPos = currentPos + movement;
        float moveDistance = movement.magnitude;

        // DEBUG: Log movement calculations
        Debug.Log($"[Projectile] {name} Movement - CurrentPos: {currentPos}, Direction: {_direction}, Speed: {speed}, Movement: {movement}, NextPos: {nextPos}");
        
        // Manual collision detection using CircleCast
        if (moveDistance > 0f)
        {
            float collisionRadius = GetCollisionRadius();
            LayerMask collisionMask = GetCollisionLayerMask();
            
            if (RaycastCollisionHelper.CircleCastCheck(currentPos, collisionRadius, _direction, moveDistance, collisionMask, out RaycastHit2D hit))
            {
                // Handle collision at hit point
                HandleCollisionEvent(hit.collider, hit.point);
                return; // Don't move if we hit something
            }
        }
        
        // Move projectile to next position
        transform.position = nextPos;
        _timeAlive += Time.deltaTime;
        
        // Check if exceeded lifetime
        if (_timeAlive >= lifetime)
        {
            SpawnDespawnParticles();
            Deactivate();
        }
    }
    
    #region ICollisionHandler Implementation
    
    public void HandleCollisionEvent(Collider2D collider, Vector2 hitPoint)
    {
        // Skip if we're chaining and this is our last target
        if (isChaining && collider.gameObject == lastTarget)
            return;
        
        // Process collision with the target
        if (ProcessCollision(collider.gameObject, hitPoint))
        {
            // Collision was processed successfully - handle special effects
            HandleSuccessfulCollision(collider, hitPoint);
        }
        else
        {
            // Check if it's a wall/environment to despawn
            LayerMask wallLayers = (1 << 12) | (1 << 15); // Wall + Environment layers
            if (((1 << collider.gameObject.layer) & wallLayers) != 0)
            {
                // Spawn impact particles for wall hits too
                SpawnImpactParticles(hitPoint);
                Deactivate();
            }
        }
    }
    
    public bool ProcessCollision(GameObject target, Vector2 position)
    {
        // Process collision - same logic as original ApplyDamage
        return ApplyDamage(target);
    }
    
    public float GetCollisionRadius()
    {
        if (_collider2D != null)
        {
            // Get radius from collider bounds
            Bounds bounds = _collider2D.bounds;
            return Mathf.Max(bounds.extents.x, bounds.extents.y);
        }
        return 0.5f; // Default radius
    }
    
    public LayerMask GetCollisionLayerMask()
    {
        return _currentLayerMask;
    }
    
    public bool IsCollisionActive()
    {
        return _isActive;
    }
    
    /// <summary>
    /// Handle successful collision with special effects (area damage, forking, chaining, piercing)
    /// </summary>
    private void HandleSuccessfulCollision(Collider2D collider, Vector2 hitPoint)
    {
        // Spawn impact particles
        SpawnImpactParticles(hitPoint);
        
        // Handle area damage
        if (hasAreaDamage)
        {
            ApplyAreaDamage(hitPoint);
        }
        
        // Handle forking (fork takes priority over chain)
        if (isFork && !hasForked)
        {
            ForkProjectiles(collider);
            hasForked = true;
            // Deactivate the original projectile after forking
            Deactivate();
            return;
        }
        
        // Handle chaining
        if (isChaining && currentChains < chainCount)
        {
            ChainToNextTarget(collider.gameObject);
            currentChains++;
            return;
        }
        
        // Handle piercing
        if (isPiercing && currentPierces < pierceCount)
        {
            currentPierces++;
            return; // Continue through target
        }
        
        // Deactivate only if we successfully hit a damageable object.
        Deactivate();
    }
    
    #endregion
    
    private bool ApplyDamage(GameObject target)
    {   
        // Safety check: Don't apply damage if we have 0 damage (indicates reset state)
        if (damage <= 0f)
        {
            return false;
        }

        // Calculate crit result - roll only once for the entire projectile lifetime (including chains)
        if (!_hasRolledCrit)
        {
            _currentCritResult = Random.Range(0f, 100f) < critChance;
            _hasRolledCrit = true;
        }
        bool isCrit = _currentCritResult;
        float finalDamage = damage; // Base damage for enemy fallback only

        // Create damage info with gem data for status effect configuration
        DamageInfo damageInfo;
        if (damageBreakdown.HasValue && damageBreakdown.Value.TotalDamage > 0f)
        {
            // Scale the damage breakdown with crit multiplier if needed
            var scaledBreakdown = damageBreakdown.Value;
            if (isCrit)
            {
                scaledBreakdown.ScaleAllDamage(critMultiplier);
            }
            
            // Use scaled damage breakdown (includes conversion info)
            damageInfo = DamageInfo.FromBreakdown(
                scaledBreakdown,
                isCrit,
                critMultiplier,
                "Projectile",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        else if (skillGemData != null)
        {
            // Player skill without valid breakdown - this should not happen!
            Debug.LogError($"[Projectile] Player skill '{skillGemData.gemName}' missing damage breakdown! Support gem effects like Brutality Support will be bypassed. HasValue: {damageBreakdown.HasValue}, TotalDamage: {(damageBreakdown.HasValue ? damageBreakdown.Value.TotalDamage : 0f)}");
            return false;
        }
        else
        {
            // Agent/Enemy skill fallback - use single damage type
            // Apply crit multiplier only for enemy fallback case
            if (isCrit)
            {
                finalDamage *= critMultiplier;
            }
            
            damageInfo = DamageInfo.FromSingleType(
                Mathf.RoundToInt(finalDamage),
                damageType,
                isCrit,
                critMultiplier,
                "Projectile",
                ailmentChance,
                skillGemData,
                supportGems
            );
        }
        
        // First, handle player damage via PlayerManager to avoid unnecessary pool lookups
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
            PlayerManager.DealDamageToPlayer(damageInfo);

            // Log damage for debugging
            string skillName = skillGemData?.gemName ?? "Projectile";
            DamageLogger.LogDamageInfo(skillName, damageInfo, "Projectile");

            return true;
        }

        // Check for CombatantHealth first (enemies use this) - use PoolManager for GC-free lookups
        if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
        {
            combatantHealth.TakeDamage(damageInfo);

            // Log damage for debugging
            string skillName = skillGemData?.gemName ?? "Projectile";
            DamageLogger.LogDamageInfo(skillName, damageInfo, "Projectile");

            return true;
        }
        // Fall back to HealthComponent for other targets - also use PoolManager
        else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
            healthComponent.TakeDamage(damageInfo);
            return true;
        }

        return false;
    }
    
    private void ApplyAreaDamage(Vector3 center)
    {
        
        // Use RaycastCollisionHelper for area damage collision detection
        LayerMask enemyLayer = 1 << 9; // Enemy layer
        Collider2D[] overlapResults = new Collider2D[32];
        int hitCount = RaycastCollisionHelper.OverlapCircleNonAlloc(center, areaRadius, overlapResults, enemyLayer);
            
        for (int i = 0; i < hitCount; i++)
        {
            var collider = overlapResults[i];
            if (collider == null) continue;
            var target = collider.gameObject;
            if (target == gameObject) continue; // Skip self
            
            // Apply reduced damage for area effect (70% of base)
            float areaDamage = damage * 0.7f;
            // Use the same critical hit result as the main projectile hit
            bool isCrit = _currentCritResult;
            if (isCrit)
            {
                areaDamage *= critMultiplier;
            }
            
            // Create damage info for area damage with gem data
            DamageInfo areaDamageInfo;
            if (damageBreakdown.HasValue)
            {
                // Scale damage breakdown for area damage
                var scaledBreakdown = damageBreakdown.Value;
                
                // Apply area damage reduction (70% of base damage)
                scaledBreakdown.ScaleAllDamage(0.7f);
                
                // Apply critical hit multiplier to the breakdown if needed
                if (isCrit)
                {
                    scaledBreakdown.ScaleAllDamage(critMultiplier);
                }

                areaDamageInfo = DamageInfo.FromBreakdown(
                    scaledBreakdown,
                    isCrit,
                    critMultiplier,
                    "Projectile_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            else if (skillGemData != null)
            {
                // Player skill without breakdown - this should not happen!
                Debug.LogError($"[Projectile] Player skill '{skillGemData.gemName}' missing damage breakdown for area damage! Support gem effects like Brutality Support will be bypassed.");
                return;
            }
            else
            {
                // Agent/Enemy skill fallback - use single damage type for area damage
                areaDamageInfo = DamageInfo.FromSingleType(
                    Mathf.RoundToInt(areaDamage),
                    damageType,
                    isCrit,
                    critMultiplier,
                    "Projectile_Area",
                    ailmentChance,
                    skillGemData,
                    supportGems
                );
            }
            
            // Handle player damage via PlayerManager
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(areaDamageInfo);

                // Log area damage for debugging
                string skillName = skillGemData?.gemName ?? "Projectile";
                DamageLogger.LogDamageInfo(skillName, areaDamageInfo, "Area Effect");
            }
            // Handle enemy damage via PoolManager - check CombatantHealth first for GC-free lookups
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var combatantHealth))
            {
                combatantHealth.TakeDamage(areaDamageInfo);

                // Log area damage for debugging
                string skillName = skillGemData?.gemName ?? "Projectile";
                DamageLogger.LogDamageInfo(skillName, areaDamageInfo, "Area Effect");
            }
            // Fall back to HealthComponent via PoolManager
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(areaDamageInfo);
            }
        }
    }
    
    private void ChainToNextTarget(GameObject currentTarget)
    {
        lastTarget = currentTarget;
        
        // Find nearest enemy within chain range using manual collision detection
        float searchRadius = 5f;
        
        // Use RaycastCollisionHelper for chain target search
        LayerMask enemyLayer = 1 << 9; // Enemy layer
        var results = RaycastCollisionHelper.GetSharedOverlapResults();
        int hitCount = RaycastCollisionHelper.OverlapCircleNonAlloc(transform.position, searchRadius, results, enemyLayer);
        
        GameObject nearestTarget = null;
        float nearestDistance = float.MaxValue;
        
        for (int i = 0; i < hitCount; i++)
        {
            var collider = results[i];
            var target = collider.gameObject;
            if (target == currentTarget) continue; // Skip current target
            if (target == gameObject) continue; // Skip self
            
            // Check if target has health (is damageable) - use PoolManager for GC-free lookups
            bool hasHealth = false;
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                hasHealth = true;
            }
            else if (PoolManager.Instance != null && 
                    (PoolManager.Instance.GetCachedComponent<CombatantHealth>(target, out var _) ||
                     PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var _)))
            {
                hasHealth = true;
            }
            
            if (hasHealth)
            {
                float distance = Vector2.Distance(transform.position, target.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestTarget = target;
                }
            }
        }
        
        if (nearestTarget != null)
        {
            // Redirect projectile to new target
            Vector2 newDirection = (nearestTarget.transform.position - transform.position).normalized;
            _direction = newDirection;
            
            // Reset lifetime for chain
            _timeAlive = 0f;
            
            // Reduce damage for each chain (80% of previous)
            damage *= 0.8f;
            
            // Scale damage breakdown to match reduced damage
            if (damageBreakdown.HasValue)
            {
                var scaledBreakdown = damageBreakdown.Value;
                scaledBreakdown.ScaleAllDamage(0.8f);
                damageBreakdown = scaledBreakdown;
            }
            
            // Update rotation
            float angle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
            transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        }
        else
        {
            // No valid targets, deactivate
            Deactivate();
        }
    }
    
    private void ForkProjectiles(Collider2D other)
    {
        if (PoolManager.Instance == null || other == null) return;
        
        Vector2 impactPoint = transform.position;
        Vector2 collisionNormal = (transform.position - other.transform.position).normalized;
        
        // Calculate offset based on the hit object's size
        float spawnOffset = 0.2f; // Base offset
        
        if (other is CircleCollider2D circleCollider)
        {
            // For circles, use radius
            spawnOffset += circleCollider.radius;
        }
        else if (other is BoxCollider2D boxCollider)
        {
            // For boxes, use the larger dimension
            spawnOffset += Mathf.Max(boxCollider.size.x, boxCollider.size.y) * 0.5f;
        }
        
        // Add a small safety margin
        spawnOffset += 0.3f;
        
        // Spawn point: offset from impact point along the collision normal
        // The normal points away from the collider we hit
        Vector2 spawnOrigin = impactPoint + (collisionNormal * spawnOffset);
        
        // Debug.Log($"Fork: impact={impactPoint}, normal={collisionNormal}, offset={spawnOffset}, spawn={spawnOrigin}");
        
        // Calculate the base angle spread
        float anglePerFork = forkCount > 1 ? forkAngle / (forkCount - 1) : 0f;
        float startAngle = -forkAngle / 2f;
        
        // Get the current projectile's direction angle
        float baseAngle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
        
        // Spawn forked projectiles
        for (int i = 0; i < forkCount; i++)
        {
            float currentAngle = baseAngle + startAngle + (anglePerFork * i);
            float radians = currentAngle * Mathf.Deg2Rad;
            Vector2 forkDirection = new Vector2(Mathf.Cos(radians), Mathf.Sin(radians));
            
            // Retrieve the original prefab for the current projectile
            GameObject projectilePrefab = PoolManager.Instance.GetOriginalPrefab(gameObject) ?? gameObject;
            
            // Spawn a new projectile from the original prefab (avoids creating a pool for 'Bullet(Clone)')
            GameObject forkedProjectile = PoolManager.Instance.Spawn(projectilePrefab, spawnOrigin, Quaternion.Euler(0, 0, currentAngle));
            if (forkedProjectile != null && PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<Projectile>(forkedProjectile, out var projectileComponent))
            {
                // Initialize the forked projectile first (sets damage, speed, position, etc.)
                float forkedDamage = damage * 0.7f;
                projectileComponent.Initialize(spawnOrigin, forkDirection, forkedDamage, gameObject.layer, speed, lifetime);

                // CRITICAL: Set status effect properties AFTER Initialize to ensure they persist
                // Initialize() doesn't override these properties, but we set them after to be safe
                projectileComponent.damageType = damageType;
                projectileComponent.ailmentChance = ailmentChance;
                projectileComponent.skillGemData = skillGemData;
                projectileComponent.supportGems = supportGems;
                projectileComponent.critChance = critChance;
                projectileComponent.critMultiplier = critMultiplier;
                
                // CRITICAL: Transfer damage breakdown to preserve support gem effects (e.g., Brutality Support)
                if (damageBreakdown.HasValue)
                {
                    var scaledBreakdown = damageBreakdown.Value;
                    // Scale the breakdown to match the forked damage (70% of original)
                    scaledBreakdown.ScaleAllDamage(0.7f);
                    projectileComponent.damageBreakdown = scaledBreakdown;
                }

                // Forked projectiles can pierce but not fork again
                if (isPiercing)
                {
                    projectileComponent.SetPiercing(true, pierceCount);
                }

                // Disable forking on the forked projectiles to prevent infinite forks
                projectileComponent.SetFork(false);
            }
        }
    }
    
    private void SpawnImpactParticles(Vector2 impactPoint)
    {
        if (!useImpactParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use impact point
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : (Vector3)impactPoint;
        
        ParticleEffectManager.Instance.SpawnParticle(impactParticleType, spawnPosition, impactParticleCount);
    }
    
    private void SpawnDespawnParticles()
    {
        if (!useDespawnParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use current position
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : transform.position;
        
        ParticleEffectManager.Instance.SpawnParticle(despawnParticleType, spawnPosition, despawnParticleCount);
    }
    
    private void Deactivate()
    {
        _isActive = false;
        
        // Stop trail particles if active
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
        
        if (PoolManager.Instance != null)
        {
        PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
        gameObject.SetActive(false);
        }
    }
    
    // ISpawnable implementation
    public void OnSpawn()
    {
        _isActive = true;
        // DEBUG: Log OnSpawn call
        Debug.Log($"[Projectile] {name} OnSpawn() called - _isActive set to: {_isActive}");
    }
    
    public void OnDespawn()
    {
        _isActive = false;
        // DEBUG: Log OnDespawn call
        Debug.Log($"[Projectile] {name} OnDespawn() called - _isActive set to: {_isActive}");
        damage = 0f; // Reset damage (will be set by Initialize)
        speed = defaultSpeed; // Reset speed
        lifetime = defaultLifetime; // Reset lifetime
        damageType = DamageType.Physical; // Reset damage type
        ailmentChance = 0f; // Reset ailment chance
        damageBreakdown = null; // Reset damage breakdown

        // Clear gem data references
        skillGemData = null;
        supportGems = null;
        
        // Reset support gem effects
        isPiercing = false;
        pierceCount = 0;
        currentPierces = 0;
        
        isChaining = false;
        chainCount = 0;
        currentChains = 0;
        lastTarget = null;
        
        isFork = false;
        forkCount = 0;
        forkAngle = 30f;
        hasForked = false;
        
        hasAreaDamage = false;
        areaRadius = 0f;
        
        // Reset critical hit tracking
        _currentCritResult = false;
        _hasRolledCrit = false;
        
        // Ensure particles are stopped
        if (ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
    }
    
    // Support gem effect setters
    public void SetPiercing(bool enable, int count = 999)
    {
        isPiercing = enable;
        pierceCount = count;
        currentPierces = 0;
    }
    
    public void SetChaining(bool enable, int count = 3)
    {
        isChaining = enable;
        chainCount = count;
        currentChains = 0;
    }
    
    public void SetFork(bool enable, int count = 2, float angle = 30f)
    {
        isFork = enable;
        forkCount = count;
        forkAngle = angle;
        hasForked = false;
    }
    
    public void SetAreaDamage(bool enable, float radius = 2f)
    {
        hasAreaDamage = enable;
        areaRadius = radius;
    }
}